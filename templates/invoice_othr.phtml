<table> 
<tr>
{default $infoText = 'Fakturujeme Vám za'}
{default $priceText = '<PERSON><PERSON><PERSON> k úhradě'}
{if empty($invoice->invcode) && $invUser->usrpayrateid == 0}
<td class="big" style="text-align: left;">Výzva k platbě</td>
<td class="big" style="text-align: right;">Variabilní symbol: {$invoice->invvarsym}</td>
{elseif empty($invoice->invcode) && $invUser->usrpayrateid > 1}
{var $infoText = 'Výpis'}
{default $priceText = 'Celkem'}
<td class="big" style="text-align: left;">Výpis služeb - NEPLAŤTE</td>
<td class="big" style="text-align: right;"></td>
{else}
<td class="big" style="text-align: left;">FAKTURA - DAŇOVÝ DOKLAD</td>
<td class="big" style="text-align: right;">ČÍSLO: {$invoice->invcode}</td>
{/if}
</tr>
</table>
<table class="border"> 
<tr>
<td style="width: 100mm;" class="right_border">
<div class="big">Dodavatel:</div>
<div class="address">
{$presenter->config["INVOICE_ADR1"]}<br />
{$presenter->config["INVOICE_ADR2"]}<br />
{$presenter->config["INVOICE_ADR3"]}<br />
{$presenter->config["INVOICE_ADR4"]}<br />
<br />
IČ: {$presenter->config["INVOICE_IC"]}<br />
DIČ: {$presenter->config["INVOICE_DIC"]}<br />
</div>
</td>
<td style="width: 100mm;">
<div class="big">Odběratel:</div>
<div class="address">
{$invoice->invrow1}<br />
{$invoice->invrow2}<br />
{$invoice->invrow3}<br />
{$invoice->invrow4}<br />
<br />
IČ: {$invoice->invic}<br />
DIČ: {$invoice->invdic}<br />
</div>
</td>
</tr>
</table>
<br>
{* Platebni podminky *}
{if !empty($invoice->invcode) || $invUser->usrpayrateid == 0}
Platební podmínky:<br>
<table class="border"> 
<tr>
<td style="width: 100mm;" class="right_border">
Datum splatnosti: {$invoice->invpaydate|date:'d.m.Y'}<br>
Datum vystavení dokladu: {$invoice->invdatec|date:'d.m.Y'}<br>
{if !empty($invoice->invcode)}
Datum uskutečnění zdaň. plnění: {$invdatevat|date:'d.m.Y'}<br>
{else}
<br>
<br>
{/if}
<br>
<br>
</td>
<td style="width: 100mm;">
Způsob úhrady: {$enum_paytypid[$invoice->invpaytypid]}<br>
Název banky: {$presenter->config["INVOICE_BANKNAME"]}<br>
{if !empty($invoice->invbankacc)}
Číslo účtu: {$invoice->invbankacc}<br>
{else}
Číslo účtu: {$presenter->config["INVOICE_BANKACC"]}<br>
{/if}
Variabilní symbol: {$invoice->invvarsym}<br>
</td>
</tr>
</table>
{/if}
{if $invoice->invtype == 'wifi'}
<br>
{elseif $invoice->invtype == 'call'}
<p>{$infoText} využívání telekomunikačních služeb za {if !empty($invperiod)}měsíce {$invperiod}{else} měsíc {$enum_monthsNames[$invoice->invmonth]} {$invoice->invyear}{/if}</p>
{else}
<p>{$infoText}</p>
{/if}
{var $sumPriceVat = 0}
{var $sumVat = 0}
{var $sumPrice = 0}
{var $vatSumary = array()}
{var $lastMonth = 0}
{foreach $invoiceItems as $row}
  {var $colspan = 7}
  {var $vatLev = (int)($row->inivat/100)}
  {var $vat = round(($row->inipricecnt*$vatLev), 2)}
  {var $sumPriceVat = $sumPriceVat + $row->inipricecnt + $vat}
  {var $sumVat = $sumVat + $vat}
  {var $sumPrice = $sumPrice + $row->inipricecnt}
  {if $invoice->ordnodph == 0 && $invoice->ordnodphrc == 0}
    {* sumarizace DPH - simplified for Latte *}
    {if !isset($vatSumary[$row->inivat])}
      {var $vatSumary[$row->inivat] = ['price' => 0, 'vat' => 0, 'pricevat' => 0]}
    {/if}
    {var $vatSumary[$row->inivat]['price'] = $vatSumary[$row->inivat]['price'] + $row->inipricecnt}
    {var $vatSumary[$row->inivat]['vat'] = $vatSumary[$row->inivat]['vat'] + $vat}
    {var $vatSumary[$row->inivat]['pricevat'] = $vatSumary[$row->inivat]['pricevat'] + $row->inipricecnt + $vat}
  {else}
    {var $colspan = 4}
  {/if}
  {if $iterator->isFirst()}
    <table class="border">
    <tr>
      <th style="text-align: left">Položka</th>
      <th style="width: 22mm;">Kusy</th>
      <th style="width: 22mm;">Cena za kus<br />bez DPH</th>
      <th style="width: 22mm;">Cena celkem<br />bez DPH</th>
      {if $invoice->ordnodph == 0 && $invoice->ordnodphrc == 0}
        <th style="width: 22mm;">Sazba</th>
        <th style="width: 22mm;">DPH</th>
        <th style="width: 22mm;">Cena<br />s DPH</th>
      {/if}
    </tr>
    <tr>
      <td colspan="{$colspan}"><hr></td>
    </tr>
  {/if}
  <tr>
    <td>{$row->initext}</td>
    <td class="items">{$row->inicnt|number:0, ',', ' '}</td>
    <td class="items">{$row->iniprice|number:2, ',', ' '}</td>
    <td class="items">{$row->inipricecnt|number:2, ',', ' '}</td>
    {if $invoice->ordnodph == 0 && $invoice->ordnodphrc == 0}
    <td class="items">{$row->inivat}</td>
    <td class="items">{$vat|number:2, ',', ' '}</td>
    <td class="items">{$row->inipricecnt+$vat|number:2, ',', ' '}</td>
    {/if}
  </tr>
  {if $iterator->isLast()}
  <tr>
    <td colspan="{$colspan}"><hr></td>
  </tr>

  <tr style="font-weight: bold;">
    <td><strong>Celkem</strong></td>
    <td class="items"></td>
    <td class="items"></td>
    <td class="items"><strong>{$sumPrice|number:2, ',', ' '}</strong></td>
    {if $invoice->ordnodph == 0 && $invoice->ordnodphrc == 0}
    <td class="items"></td>
    <td class="items"><strong>{$sumVat|number:2, ',', ' '}</strong></td>
    <td class="items"><strong>{$sumPriceVat|number:2, ',', ' '}</strong></td>
    {/if}
  </tr>
  </table>
  {/if}
{/foreach}
{if $invoice->ordnodph == 0 && $invoice->ordnodphrc == 0}
{else}
  {var $invoice->invpricevat = $invoice->invprice}
{/if}
<br>
<table style="width: 100mm; text-align: center; margin-left: 100mm;" class="border">
  {if $invoice->ordnodph == 0 && $invoice->ordnodphrc == 0}
  <tr>
    <td style="width: 25mm;">Sazba</td>
    <td style="width: 25mm;">bez daně</td>
    <td style="width: 25mm;">DPH</td>
    <td style="width: 25mm;">s daní</td>
  </tr>
  {foreach $vatSumary as $key => $dphItem}
  <tr>
    <td>{$key}%</td>
    <td>{$dphItem["price"]|number:2, ',', ' '} Kč</td>
    <td>{$dphItem["vat"]|number:2, ',', ' '} Kč</td>
    <td>{$dphItem["pricevat"]|number:2, ',', ' '} Kč</td>
  </tr>
  {/foreach}
  <tr>
    <td colspan="4"><hr><br></td>
  </tr>
  {/if}
  <tr>
    <td style="text-align: left;"  colspan="2"><strong>Celkem:</strong></td>
    <td  style="text-align: right;" colspan="2"><strong>{$invoice->invpricevat|number:0, ',', ' '},00 Kč</strong></td>
  </tr>
  {if $invoice->invstatus==0}
  {var $invpricevat = $invoice->invpricevat}
  {foreach $invoicePayments as $payment}
  {if !empty($payment->invpayeddate)}
  {var $invpricevat = $invpricevat - $payment->invpricevat}
  <tr>
    <td style="text-align: left;" colspan="3">Uhrazeno {$payment->invpayeddate|date:'d.m.Y'}<br >platba VS {$payment->invvarsym}:</td>
    <td style="text-align: right;">-{$payment->invpricevat|number:0, ',', ' '},00 Kč</td>
  </tr>
  {/if}
  {/foreach}
  
  <tr style=" font-size: 16px">
    <td style="text-align: left;" colspan="3"><strong>{$priceText}:</strong></td>
    <td style="text-align: right;"><strong>{$invpricevat|number:0, ',', ' '},00 Kč</strong></td>
  </tr>
  {if $invoice->invpaytypid == 3}
  <tr style=" font-size: 16px; color: red;">
    <td colspan="4"><strong>PLATBA BUDE PROVEDENA INKASEM Z VAŠEHO ÚČTU, NEPLAŤTE!</strong></td>
  </tr>
  {/if}
  {else}
  <tr>
    <td style="text-align: left;" colspan="2">Uhrazeno {$invoice->invpayeddate|date:'d.m.Y'}<br >platba VS {$invoice->invvarsym}:</td>
    <td style="text-align: right;" colspan="2">-{$invoice->invpricevat|number:0, ',', ' '},00 Kč</td>
  </tr>
  <tr style=" font-size: 16px">
    <td style="text-align: left;" colspan="2"><strong>{$priceText}:</strong></td>
    <td style="text-align: right;" colspan="2"><strong>0,00 Kč</strong></td>
  </tr>
  <tr style=" font-size: 16px; color: red;">
    <td colspan="4"><strong>NEPLAŤTE, JIŽ BYLO UHRAZENO</strong></td>
  </tr>
  {/if}
</table>
{if $invoice->ordnodph == 1}
<p>Jedná se o dodání zboží do jiného členského státu dle par.64 odst.1) ZDPH. Daň odvede zákazník.</p>
{elseif $invoice->ordnodphrc == 1}
<p>Reverse charge - výši daně je povinen doplnit a přiznat odběratel.</p>
{/if}
{* EET section removed *}
{if !empty($invoice->invcode)}
<br> 
<p align="right" style="padding-right: 20mm;"><img src="{$baseUri}/razitko.jpg" /></p>
{/if}