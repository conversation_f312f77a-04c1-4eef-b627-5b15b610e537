{block #content}
  <h3>{block #title}Generovat faktury{/block}</h3>
  {control genInvoicesForm}

  <h3>Generuj faktury na vyfakturuj.cz</h3>
  {control vyfakturujInvoicesForm}

  <h3>Generovat inkaso</h3>
  {control genInkasoForm}

  <h3>Mailovat výzvy k platbě</h3>
  {control mailInvoicesForm}

  <h3>Import plateb z banky</h3>
  <p><a href="{plink :Front:Script:pairPayments 'k'=>'942a13a8cb5840'}">Spustit párování plateb</a></p>
  
  {foreach $invoicesNotPayed as $row}
    {if $iterator->isFirst()}
    <h4>Neuhrazené výzvy k platbě</h4>
    <table class="grid">
    <tr>
      <th></th>
      <th>Č. fa</th>
      <th>Var.sym.</th>
      <th>Mesí<PERSON>/rok</th>
      <th>Po splatnosti</th>
      <th><PERSON><PERSON>tka</th>
      <th colspan="4">Adresa</th>
      <th>Telefon</th>
      <th>Mail</th>
      <th>&nbsp;</th>
      <th>&nbsp;</th>
      <th>&nbsp;</th>
    </tr>  
    {/if}
    <tr>
      <td><img src="{$baseUri}/ico/{$row->invtype}.png" width="16" height="16" /></td>
      <td>{$row->invcode}</td>
      <td>{$row->invvarsym}</td>
      <td>{$row->invmonth}/{$row->invyear}</td>
      <td>{$row->invduedateafter}</td>
      <td style="text-align: right;">{$row->invpricevat|number:0, ',', ' '} Kč</td>
      <td>{$row->invrow1}</td>
      <td>{$row->invrow2}</td>
      <td>{$row->invrow3}</td>
      <td>{$row->invrow4}</td>
      <td>{$row->invphone}</td>
      <td>{$row->invmail}</td>
      <td>
      <a href="{plink getInvoice $row->invid, 'D'}"><img src="{$baseUri}/ico/pdf.png" width="16" height="16" alt="export do PDF" title="Stáhnout PDF" /></a>
      <a href="{plink getInvoice $row->invid, 'I'}" target="pdf"><img src="{$baseUri}/ico/magnifier.png" width="16" height="16" alt="otevřít PDF" title="otevřít PDF" /></a>

      {if !empty($row->invvyfurl)}
        <a href="{$row->invvyfurl}"><img src="{$baseUri}/ico/front.png" width="16" height="16" alt="faktura" title="faktura" target="_blank" /></a>
      {/if}

      </td>
      <td>{if !empty($row->invpayeddate)}{$row->invpayeddate|date:'d.m.Y'}{else}<a href="{plink Invoice:payInvoice $row->invid}">ZAPLATIT</a>{/if}</td>
      <td>
      <a href="{plink User:edit $row->invusrid}"><img src="{$baseUri}/ico/user.png" width="16" height="16" alt="editace klienta" title="editace klienta" /></a>
    </td>
    </tr>  
    {if $iterator->isLast()}
    </table>
    {/if}
  {/foreach}
  
  {foreach $invoicesForPrint as $row}
    {if $iterator->isFirst()}
    <h3>Tisk faktur do PDF</h3>
    <table class="grid">
    {/if}
    <tr>
    <td>{$row->invmonth}/{$row->invyear}</td>
    <td>{$row->invpricevat|number:2, ',', ' '}</td>
    <td><a href="{plink getInvoices $row->invmonth, $row->invyear}"><img src="{$baseUri}/ico/pdf.png" width="16" height="16" alt="export do PDF" title="Stáhnout PDF" /></a></td>
    </tr>
    {if $iterator->isLast()}
    </table>
    {/if}
  {/foreach}
{/block}