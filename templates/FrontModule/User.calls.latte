{var $title = 'Moje volání'}
{var $robots = noindex}

{block #content}

<div class="text">
<h2>Výpisy volání pro tyto čísla:
{foreach $numbers as $number}
  {if $iterator->isFirst()}
  <div id="submenu">  
  {/if}
  <span><a n:class="$presenter->isLinkCurrent('calls', ['num' => $number]) ? 'current'" href="{plink calls 'num'=>$number}">{$number|formatPhoneNumer}</a></span>
  {if $iterator->isLast()}
  </div>
  {/if}
{/foreach}
</div>
</h2>
{ifset $lists}
{foreach $lists as $key => $row}
  {if $iterator->isFirst()}
  <small>Klikněte na název měsíce pro který hcete výpis zobrazit.</small>
  <table class="grid">
  <tr>
    <th>rok</th>
    <th>měs<PERSON>ce</th>
  </tr>
  {/if}
  <tr>
    <td>{$key}</td>
    <td>
    {foreach $row as $mkey=>$mrow}
    <a href="{plink calls, 'num'=>$curNumber, 'mon'=>$mkey, 'yea'=>$key}">{$enu_months[$mkey]}</a>{if !$iterator->isLast()}, {/if}
    {/foreach}
    </td>
  </tr>
  {if $iterator->isLast()}
  </table>
  {/if}
{/foreach}
{ifset $calls}
{foreach $calls as $row}
  {if $iterator->isFirst()}
  <h3>Výpis číslo {$curNumber|formatPhoneNumer} {ifset $curYear}{$enu_months[$curMonth]} {$curYear}{/ifset}</h3>
  <table class="grid">
  <tr>
    <th>Služba</th>
    <th>Datum, čas</th>
    <th>Cílové číslo</th>
    <th>Počet jednotek</th>
    <th>Jednotka</th>
  </tr>
  {/if}
  <tr>
    <td>{$row->calservice}</td>
    <td>{$row->caldatetime|date:'d.m.Y H:i:s'}</td>
    <td> {$row->caltargetnumber}</td>
    <td>{$row->calduration}</td>
    <td>{$row->calunit}</td>
  </tr>
  {if $iterator->isLast()}
  </table>
  {/if}
{/foreach}
{/ifset}
{/ifset}
{/block}