<?php

/**
 * Base class for all application presenters.
 *
 * <AUTHOR>
 * @package    MyApplication
 */
abstract class BasePresenter extends Nette\Application\UI\Presenter {
  /** nastaveni serveru */ 
  public $config = array();

  /** nastaveni operátorů */
  public $configOperators = array();

    /**
   * @var \TemplateFilters @inject
   */
  public $myTemplateFilters;

  /** nastaveni z neonu */
  public $neonParameters = array();

  /** @var \Nette\Mail\Mailer @inject */
  public $mailer;

  /** @var Nette\Http\SessionSection */
  public $appSession;
  protected $backinkSave = true;

  public function injectNeonParametersRepository(Classes\NeonParametersRepository $paramRepository) {
    $this->neonParameters = $paramRepository->getParameters();
  }

  protected function startup() {
    parent::startup();

    // nastartujeme session
    $this->appSession = $this->session->getSection("app");
    
    //naplnim uzivatelske nastaveni do cache
    $result = dibi::query('SELECT * FROM config');
    $arr = $result->fetchPairs('cfgcode', 'cfgvalue');
    $this->config = $arr;

    $this->configOperators = $this->neonParameters["operators"];

    // In Nette 3.x, mailer should be configured via DI container
    // For now, we'll create it manually but this should be moved to config.neon
    $this->mailer = new Nette\Mail\SmtpMailer([
      'host' => 'mailproxy.webglobe.com',
      'port' => '465',
      'username' => '<EMAIL>',
      'password' => '_Gmu@xF6_W3gt9aR',
      'secure' => 'ssl',
    ]);
  }
  
  protected function beforeRender() {
    $this->template->config = $this->config;
    // Custom filters are registered in config.neon
    // uložíme request
    if ($this->backinkSave) $this->appSession->backlink = $this->storeRequest();
  }
  
  /**
   * Formats view template file names.
   * @return array
   */
  public function formatTemplateFiles(): array {
    $root = WWW_DIR . '/../templates'; // Use constant instead of Environment
    $name = $this->getName();
    $presenter = substr($name, strrpos(':' . $name, ':'));
    $module = substr($name, 0, (int) strrpos($name, ':')).'Module';

    return array(
      "$root/$module/$presenter.$this->view.latte",
    );
  }
  
  /**
   * Formats layout template file names.
   * @return array
   */
  public function formatLayoutTemplateFiles(): array {

    $root = WWW_DIR . '/../templates'; // Use constant instead of Environment
    $name = $this->getName();
    $presenter = substr($name, strrpos(':' . $name, ':'));
    $module = substr($name, 0, (int) strrpos($name, ':')).'Module';
    $layout = $this->layout ? $this->layout : 'layout';    
    return array(
      "$root/$module/@$layout.latte",
      "$root/@$layout.latte",
    );
  }
  
  public function createTemplate(): Nette\Application\UI\Template {
    $template = parent::createTemplate();
    // $template->registerHelper('texy', [new MyTexy($template->baseUrl), 'process']); // Commented out due to Texy compatibility


    // Register custom filters for Latte 3.x
    $template->addFilter('formatPhoneNumer', function($value) {
      $value = substr($value, -9);
      $value = substr($value, 0, 3)." ".substr($value, 3, 3)." ".substr($value, 6, 3);
      return $value;
    });
    $template->addFilter('getPriceNoVat', function($priceVat, $vatid=0, $precision=0) {
      $koef[0] = 0.1736; //zakladni
      $koef[1] = 0.1304; //snizena
      $koef[2] = 0.0909; //treti
      $vat = $priceVat * $koef[$vatid];
      return round($priceVat - $vat, $precision);
    });

    $template->config = $this->config;
    $template->baseUri = $this->getHttpRequest()->getUrl()->getBaseUrl(); // Fix for undefined $baseUri
    return $template;
  }
  
  protected function createAppForm() {
    $form = new Nette\Application\UI\Form;
    return $form;
  }
  
  protected function sendMail($mailTo, $subject, $bodyTemplate) {
    if (empty($mailTo)) return false;
    $mail = new Nette\Mail\Message();
    $mail->setFrom($this->config["SERVER_MAIL"], $this->config["SERVER_NAME"]);
    $mail->addTo($mailTo);
    $mail->setSubject($subject." - ".$this->config["SERVER_NAME"]);
    $mail->setHtmlBody($bodyTemplate);

    $mailer = new Nette\Mail\SmtpMailer([
      'host' => 'mailproxy.webglobe.com',
      'port' => '465',
      'username' => '<EMAIL>',
      'password' => '_Gmu@xF6_W3gt9aR',
      'secure' => 'ssl',
    ]);

    $mailer->send($mail);
  }
  
  protected function sendSms($gsm, $bodyTemplate) {
    $text = (string)$bodyTemplate;

    //nacteni nastaveni pristupu SMS
    $sms = $this->neonParameters['sms'];

    if ($sms["login"] != "") {
      include_once LIBS_DIR.'/smsmanager/SMSManager.php';
      $sms = new \SMSManager\SMSManager($sms["login"], $sms["passw"]);
      
      $gsm = trim($gsm);
      $gsm = str_replace(' ', '', $gsm);
      $gsm = substr($gsm, -9);
      $gsm = '00420'.$gsm;

      $text = \Nette\Utils\Strings::fixEncoding($text);
      $text = \Nette\Utils\Strings::toAscii($text);

      $message = $sms->prepareMessage($gsm, $text);
      //\Tracy\Debugger::log($message);
      $response = $sms->send($message);
      //\Tracy\Debugger::log($response);
    } else {
      return(TRUE);
    }  
  }
  
  protected function prepareSms($gsm, $bodyTemplate) {
    $text = (string)$bodyTemplate;

    //nacteni nastaveni pristupu SMS
    $sms = $this->neonParameters['sms'];

    if ($sms["login"] != "") {
      include_once LIBS_DIR.'/smsmanager/SMSManager.php';
      $sms = new \SMSManager\SMSManager($sms["login"], $sms["passw"]);
      
      $gsm = trim($gsm);
      $gsm = str_replace(' ', '', $gsm);
      $gsm = substr($gsm, -9);
      $gsm = '00420'.$gsm;
      
      return $sms->prepareMessage($gsm, $text);
    } else {
      return(NULL);
    }  
  }
  
  protected function sendSmsList($messages) {
    include_once LIBS_DIR.'/smsmanager/SMSManager.php';
    $sms = new \SMSManager\SMSManager(SMSAPI_LOGIN, SMSAPI_PASSW);  
    $response = $sms->send($messages);
  }
  
  public function createComponentCss() {
    // připravíme seznam souborů
    // FileCollection v konstruktoru může dostat výchozí adresář, pak není potřeba psát absolutní cesty
    $files = new \WebLoader\FileCollection(WWW_DIR . '/css');
  
    // kompilátoru seznam předáme a určíme adresář, kam má kompilovat
    $compiler = \WebLoader\Compiler::createCssCompiler($files, WWW_DIR . '/webtemp');

    // nette komponenta pro výpis <link>ů přijímá kompilátor a cestu k adresáři na webu
    return new \WebLoader\Nette\CssLoader($compiler, $this->template->basePath . '/webtemp');
  }
  
  public function createComponentJs() {
    $files = new \WebLoader\FileCollection(WWW_DIR . '/js');
    $compiler = \WebLoader\Compiler::createJsCompiler($files, WWW_DIR . '/webtemp');
    return new \WebLoader\Nette\JavaScriptLoader($compiler, $this->template->basePath . '/webtemp'); 
  }
  
  public function getInvoice($invoice, $dest="I", $mpdf = Null) {
    $template = $this->getTemplate();
    $invoices = New \Model\InvoicesModel();
    $template->invoice = $invoice;

    // EET functionality removed
    $template->eet = NULL;

    $template->invUser = dibi::fetch("SELECT * FROM users WHERE usrid=%i", $invoice->invusrid);
    if ($invoice === false)  throw new Nette\Application\BadRequestException('Stránka nenalezena', '404');
    $template->invoiceItems = dibi::fetchAll("SELECT * FROM invoiceitems WHERE iniinvid=%i", $invoice->invid, " ORDER BY iniyear, inimonth, iniid");
    $template->invoicePayments = dibi::fetchAll("SELECT * FROM invoices WHERE invinvid=%i", $invoice->invid, " ORDER BY invid");
    $arr = array();
    foreach ($template->invoiceItems as $row) {
      $arr[$row->inimonth] = $row->inimonth;  
    }
    $template->enum_monthsNames = $invoices->getEnumMonId();
    if (count($arr) == 1) {
      $template->invperiod = ""; 
    } else {
      $template->invperiod = implode(',', $arr).'/'.$invoice->invyear;;
    }
    
    
    $template->enum_paytypid = $invoices->getEnumPayTypId();
    $template->invdatevat = dibi::fetchSingle("SELECT LAST_DAY('".$invoice->invyear."-".$invoice->invmonth."-1')");
    $template->setFile(APP_DIR.'/../templates/invoice'.($invoice->invtype == 'othr' ? '_othr' : '').'.phtml');
    $fname = ('faktura-'.$invoice->invcode.'-'.Nette\Utils\Strings::webalize($invoice->invrow1));
    
    $template->headers = (object) NULL;
    $pdfHtml = (string) $template; // vyrenderujeme šablonu už nyní
    
    // mPDF
    if (empty($mpdf)) {
      require_once  LIBS_DIR.'/../vendor/autoload.php';
      $mpdf = new \mPDF('utf-8','A4', 12,'',10,10,10,10,9,9,'P');
      $mpdf->useOnlyCoreFonts = true;
      
      $stylesheet = file_get_contents(APP_DIR.'/../templates/invoice.css');
      $mpdf->WriteHTML($stylesheet,1);
      $mpdf->SetDisplayMode('real');
      $mpdf->useOnlyCoreFonts = true;
      $mpdf->autoScriptToLang = true;
      $mpdf->autoLangToFont = true;
      $mpdf->AddPage('P');
      $mpdf->WriteHTML($pdfHtml, 2);
      if ($dest=="I") {
        $name = Nette\Environment::getVariable("tempDir")."/".$fname.".pdf";
      } else {
        $name = $fname.".pdf";
      }  
      $mpdf->Output($name, $dest);  
    } else {
      $mpdf->AddPage('P');
      $mpdf->WriteHTML($pdfHtml, 2);
      return $mpdf;
    }  
  }
  
  public function getContract($usrid, $dest="I", $mpdf = Null) {
    $template = $this->getTemplate();
    $contracts = new \Model\ContractsModel();
    
    $contract = dibi::fetch("SELECT * FROM users 
      INNER JOIN contracts_log ON (colusrid=usrid)
      INNER JOIN contracts ON (colconid=conid)
      WHERE 
      contype='contract' AND 
      colusrid=%i", $usrid, " ORDER BY colid DESC");
    if ($contract === false)  throw new Nette\Application\BadRequestException('Stránka nenalezena', '404');
    $template->contract = $contract;
    
    $template->contractUser = dibi::fetch("SELECT * FROM users WHERE usrid=%i", $contract->colusrid);
    $template->contractUserTels = dibi::fetchAll("SELECT * FROM numbers WHERE numusrid=%i", $contract->colusrid);
    if ($contract === false)  throw new Nette\Application\BadRequestException('Stránka nenalezena', '404');
    $template->setFile(APP_DIR.'/../templates/contract.phtml');
    
    $contractCode = $contracts->getCode($contract->colusrid, $contract->colid);
    $template->contractCode = $contractCode;
    $fname = ('smlouva-'.$contractCode.'-'.Nette\Utils\Strings::webalize($contract->usrirow1));
    
    $template->headers = (object) NULL;
    $pdfHtml = (string) $template; // vyrenderujeme šablonu už nyní
    
    // mPDF
    if (empty($mpdf)) {
      require_once  LIBS_DIR.'/../vendor/autoload.php';
      $mpdf = new \mPDF('utf-8','A4', 8,'',15,15,15,20,9,9,'P');
      $mpdf->useOnlyCoreFonts = true;
      $mpdf->SetHTMLFooter('<p style="text-align: right; font-size: small; font-style: italic; border-top: 1px solid black; padding-top: 10px;">Smlouva č. '.$contractCode.', strana: {PAGENO}<p>');
      $stylesheet = file_get_contents(APP_DIR.'/../templates/contract.css');
      $mpdf->WriteHTML($stylesheet,1);
      $mpdf->SetDisplayMode('real');
      $mpdf->autoScriptToLang = true;
      $mpdf->autoLangToFont = true;
      $mpdf->AddPage('P');
      $mpdf->WriteHTML($pdfHtml, 2);
      if ($dest=="I") {
        $name = Nette\Environment::getVariable("tempDir")."/".$fname.".pdf";
      } else {
        $name = $fname.".pdf";
      }  
      $mpdf->Output($name, $dest);  
    } else {
      $mpdf->AddPage('P');
      $mpdf->WriteHTML($pdfHtml, 2);
      return $mpdf;
    }  
  }
}
