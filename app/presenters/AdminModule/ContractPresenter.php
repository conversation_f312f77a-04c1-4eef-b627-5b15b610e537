<?php
namespace AdminModule;
use dibi;
use Nette;

final class ContractPresenter extends BasePresenter {
  
  public function editFormSubmitted (Nette\Application\UI\Form $form) {
    
    if ($form->isSubmitted()) {
      
      $contract = new \Model\ContractsModel();
      $id = (int)$this->getParameter('id');
      $vals = $form->getValues(); 
    
    if ($id > 0) {
      //zkontroluji jestli neni prirazena nejakemu klientovi
      $cnt = (int)\dibi::fetchSingle("SELECT COUNT(*) FROM contracts_log WHERE colconid");
      if ($cnt > 0) {
        $this->flashMessage("Smlouva je již př<PERSON>řazena klientům, nelze ji už editovat.", 'err');
        $this->redirect('edit', $id);
      }
    }
    
      try {
        if ($contract->save($id, $vals)) {
          $this->flashMessage('Uloženo v pořádku');
          $this->redirect('Contract:default');        
        }
        
      } catch (ModelException $e) {
        $form->addError($e->getMessage());
      }    
    }
  } 
  
  /********************* view default *********************/ 
  
  public function renderDefault() {
    $contract = new \Model\ContractsModel();
    $dataRows = dibi::query("SELECT * FROM contracts ORDER BY conid DESC")
      ->fetchAssoc('conid');
    
    $this->template->dataRows = $dataRows;
    $this->template->enum_contype = $contract->getEnumConType();  
  }
  
  public function renderEdit($id) {
    $form = $this['editForm'];
    
    if (!$form->isSubmitted()) {
      $contract = new \Model\ContractsModel();
      $dataRow = array();
      if ($id > 0) {
        $dataRow = $contract->load($id);
        if (!$dataRow) {
          throw new Nette\Application\BadRequestException('Záznam nenalezen');
        }
        $form->setDefaults($dataRow);
      }
      $this->template->dataRow = $dataRow; 
      $this->template->id = $id; 
      
      $this->template->enum_contype = $contract->getEnumConType();
    }
  }
  
  /********************* facilities *********************/

  protected function createComponentEditForm() {
    $id = (int)$this->getParameter('id');
    
    $contract = new \Model\ContractsModel();
    
    $form = new Nette\Application\UI\Form();
    
    $form->addSelect('contype', 'Typ stránky:', $contract->getEnumConType());
    $form->addSelect('conmasid', 'Dodatek ke smlouvě:', $contract->getEnumConMasId())
      ->setOption('description', ' Vyplňte jen v případě dodatku')
      ->setPrompt("")
      ->addConditionOn($form['contype'], Nette\Forms\Form::EQUAL, 'add')
        ->addRule(Nette\Forms\Form::FILLED, 'U dodatku musíte vyplnit k jaké smlouvě se vztahuje.');
    
    $form->addText('conname', 'Název:', 30)
      ->addRule(Nette\Forms\Form::FILLED, 'Název: je nutné vyplnit');
    
    $form->addTextArea('conbody', 'Text:', 60, 20);
    $form['conbody']->getControlPrototype()->class('texyla'); 
    
    $form->addSubmit('save', 'Uložit');
    $form->onSuccess[] = array($this, 'editFormSubmitted');

    $form->addProtection('Prosím odešlete formulář znovu (platnost formuláře již vypršela).');
    
    return $form;  
  } 
}  
?>