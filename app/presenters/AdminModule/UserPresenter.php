<?php
namespace AdminModule;
use dibi;
use Model\InvoicesModel;
use Model\UsersModel;
use Nette;

final class UserPresenter extends BasePresenter {

  /** @persistent */
  public $sPhone = '';

  /** @persistent */
  public $sName = '';

  /** @persistent */
  public $sPayTypId;
  
  /** @persistent */
  public $sH = 0;

  /** @persistent */
  public $sNp = 0;

  public $backlink = '';
  
  public function userEditFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form['save']->isSubmittedBy()) {
      $users = new \Model\UsersModel();
      $vals = $form->getValues();
      $id = $this->getParameter('id');

      if ($id > 0) {
        //kontrola duplicity tel. cisla
        $cnt = dibi::fetchSingle("SELECT COUNT(*) FROM users WHERE usrtype=%s", $this->sec, " AND usrphone=%s", $vals["usrphone"], " AND usrid!=%i", $id);
        if ($cnt > 0) {
          $form->addError("Duplicita zadaného tel. čisla. Už existuje účet s tímto tel.číslem.");
          return;
        }  
        $users->update($id, $vals);  
        $this->flashMessage('Aktualizováno v pořádku');
      } else {
        //kontrola duplicity tel. cisla
        $cnt = dibi::fetchSingle("SELECT COUNT(*) FROM users WHERE usrtype=%s", $this->sec, " AND usrphone=%s", $vals["usrphone"]);
        if ($cnt > 0) {
          $form->addError("Duplicita zadaného tel. čisla. Už existuje účet s tímto tel.číslem.");
          return;
        }
        $vals->usrpassword = md5($vals->usrname.$vals->usrmail);
        $id = $users->insert($vals);
        $this->flashMessage('Nový záznam uložen v pořádku');
      }
    }
    $this->redirect('edit', $id);
  } 
  
  public function userNumbersEditFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form['save']->isSubmittedBy()) {
      $someErr = False;
      $numbers = new \Model\NumbersModel();
      $id = (int)$this->getParameter('id');
      if ($id > 0) {
        $frmVals = $form->getValues();
        
        foreach ($frmVals["data"] as $key => $data) {
          //kontrola zda uz neexistuje to cislo
          $where = "";
          if ($key == 0 && empty($data->numnumber)) continue;
          if ($key > 0) {
            $where = " AND numusrid!=$id ";
          }
          $row = dibi::fetch("SELECT users.* FROM numbers INNER JOIN users ON (usrid=numusrid) WHERE numnumber=%s", $data->numnumber, $where);
          if ($row) {
            $this->flashMessage('Pozor číslo '.$data->numnumber.' je již přiřazeno u klienta '.$row->usrname." (id: $row->usrid). K duplicitě může dojít u jednoho klienta, pokud klient přechází od jednoho operátora k druhému v průběhu měsíce.", 'err');
            //$someErr = True;
            //continue;
          }
          if (!empty($data->numdatefrom)) {
            $data->numdatefrom = $this->formatDate($data->numdatefrom);
          } else {
            $data->numdatefrom = NULL;
          }  
          if (!empty($data->numdateto)) {
            $data->numdateto = $this->formatDate($data->numdateto);
          }  else {
            $data->numdateto = NULL;
          }
          if ($key == 0) {
            $data["numusrid"] = $id;
            $numbers->insert($data);  
          } else {
            //zjistim jestli doslo ke zmene tarifu
            //puvodni tarif
            $pliid = (int)dibi::fetchSingle("SELECT numpliid FROM numbers WHERE numid=%i", $key);
            $numbers->update($key, $data);  
            if (FALSE) {
            //if ($data->numpliid != $pliid) {
              //doslo ke zmene tarifu
              $plis = new \Model\PricelistsModel();
              $usrs = new \Model\UsersModel();
    
              $template = $this->createTemplate();
              $template->priceList = $plis->load($data->numpliid);
              $template->user = $usrs->load($id);
              $template->phoneNumber = $data->numnumber;

              //service email podle ceníku
              $operatorMail = "";
              if (isset($this->configOperators[$template->priceList->pliserid])) {
                $operatorMail = $this->configOperators[$template->priceList->pliserid]["email"];
              }

              $template->setFile(WWW_DIR . '/../templates/mailNewTarif.phtml');

              try {
                $this->sendMail($this->config["SERVER_MAIL"], 'Telcall - požadavek na změnu tarifu', $template);

                if ($operatorMail !== "") {
                  $this->sendMail($operatorMail, 'Telcall - požadavek na změnu tarifu', $template);
                }

                $this->flashMessage("Byl odeslán email s požadavkem na změnu tarifu operátorovi.");
              } catch (Nette\Security\AuthenticationException $e) {
                $someErr = True;
                $this->flashMessage("Odeslání emailu s požadavkem na změnu tarifu se NEZDAŘILO.", 'err');
              }
            }
          } 
        }
      }
      if ($someErr) {
        $this->flashMessage('Nastala nějaká chyba', 'err');
      } else {
        $this->flashMessage('Seznam čísel aktualizován bez chyb');  
      }
      
    }
    $this->redirect('this');
  }
  
  /********************* view default *********************/ 
  
  public function renderDefault() {
    
    $users = new \Model\UsersModel();
    $h = (int)$this->sH;
    $nP = (bool)$this->sNp;
    $where = "WHERE (usrtype='".$this->sec."' OR usrtype IS NULL) AND usrstatus IN (".($h==1 ? '1' : '0,2').")";
    if (!empty($this->sName)) $where .= " AND (usrname LIKE '%$this->sName%') ";
    if (!empty($this->sPayTypId)) $where .= " AND (usrpaytypid=$this->sPayTypId) ";
    if (!empty($this->sPhone)) $where .= " AND EXISTS (SELECT * FROM numbers WHERE numusrid=usrid AND numnumber LIKE '420$this->sPhone%') ";
    $dataRows = $users->fetchAll("SELECT * FROM users $where ORDER BY usrname");
    $this->template->dataRows = $dataRows;           
    
    //ciselnik statusu
    $this->template->enum_usrstatus = $users->getEnumUsrStatus();
    
    $pricelists = new \Model\PricelistsModel();
    //ciselnik ceniku
    $this->template->enum_pricelsits = $pricelists->getEnumPliId();

    if ($nP) {
      //načtu všechny neuhrazené doklady po klientech
      $this->template->notPayedByClient = dibi::query("SELECT * FROM invoices WHERE invstatus=0")->fetchAssoc("invusrid,invid");

    }
  }
  
  public function renderEdit($id) {
    $form = $this['userEditForm'];
    
    if (!$form->isSubmitted()) {
      $users = new \Model\UsersModel();
      if ($id >0) {
        $dataRow = $users->load($id);
        if (!$dataRow) {
          throw new Nette\Application\BadRequestException('Záznam nenalezen');
        }

        $form->setDefaults($dataRow);
        $this->template->dataRow = $dataRow; 
        $this->template->numbers = dibi::fetchAll("SELECT * FROM numbers WHERE numusrid=%i", $dataRow->usrid);
        
        $this->template->contracts = dibi::fetchAll("
          SELECT * FROM contracts_log
          INNER JOIN contracts ON (conid=colconid) 
          WHERE coldatesigned IS NULL AND colusrid=%i", $id);
        
        
        $this->template->invoices = dibi::fetchAll("
          SELECT invoices.*, DATEDIFF(CURDATE(),invpaydate) AS invduedateafter,usrpayrateid 
          FROM invoices 
          INNER JOIN users ON (usrid=invusrid)
          WHERE invusrid=%i", $dataRow->usrid, " ORDER BY invyear DESC, invmonth DESC LIMIT 18");
      }
    }
  }
  
  public function renderTacFinshed($id) {
    $tacs = new \Model\TarifchangesModel();
    //nastavim novy tarif
    $tac = $tacs->load($id);
    $numbers = new \Model\NumbersModel();
    //zjistim numid
    $numid = \dibi::fetchSingle("SELECT numid FROM numbers WHERE numusrid=%i", $tac->tacusrid, " AND numnumber=%s", $tac->tacnumber);
    $numbers->update($numid, array(
      'numpliid'=>$tac->tacpliid,
    ));
    $tacs->update($id, array(
      'tacstatus'=>1,
    ));
    
    
    $this->flashMessage("Změna provedena");
    $this->redirect('Admin:default');
  }
  
  public function renderDetail($id) {
    $id = (int)$id;
    $this->template->usrDetail = dibi::fetch("SELECT * FROM users WHERE usrid=$id");
  }
  
  public function renderDelete($id) {
    if ($id > 0) {
      $users = new \Model\UsersModel();
      $users->delete($id);
      $this->flashMessage('Zákazník byl vymazán');
    }
    $this->redirect('default');           
  }
  
  
  public function actionDeleteNumber($numid, $usrid) {
    if ($numid > 0) {
      $numbers = new \Model\NumbersModel();
      $numbers->delete($numid);
      $this->flashMessage('Záznam byl vymazán'); 
    }
    $this->redirect('edit', $usrid);           
  }
  
  public function actionGetContract($usrid, $dest="I") {
    //vraci template prislusne
    $this->getContract($usrid, $dest);
  }
  
  /********************* facilities *********************/

  protected function createComponentUserEditForm() {
    $pricelists = new \Model\PricelistsModel();
    $invoices = new \Model\InvoicesModel();
    $usrs = new \Model\UsersModel();
    $form = new Nette\Application\UI\Form();

    $id = (int)$this->getParameter("id");

    if ($id > 0) $usr = $usrs->load($id);

    $form->addGroup("Základní údaje");

    $form->addSelect("usrtype", "Typ účtu", $usrs->getEnumUsrType())
      ->setDefaultValue($this->sec)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte typ účtu.');

    $form->addText('usrdiscount', 'Sleva bez DPH:', 30);

    $form->addText('usrname', 'Jméno, příjmení:', 30)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte jméno, příjmení.');

    $form->addText('usrmail', 'Email:', 30)
      ->setEmptyValue('@')
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::EMAIL, 'Email nemá správný formát');  
    
    $form->addText('usrphone', 'Telefon:', 30)
      ->setOption('description', ' slouží klientovi zároveň jako přihlašovací jméno')
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte telefon.');

    $form->addText('usrinetprice', 'Měsíční poplatek za internet:', 30)
      ->setOption('description', ' pokud vyplníte hodnotu větší než 0 vloží se do faktury navíc položka za připojení na internet v této výši');

    $form->addSelect("usrpaytypid", "Způsob úhrady", $invoices->getEnumPayTypId())
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte způsob úhrady.');

    $form->addSelect("usrpayrateid", "Jak často fakturovat:", $invoices->getEnumPayRateId($this->sec))
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label.');
    if ($this->sec == "call") {

    } else {
      $form->addText("usrpricewifi", "Paušál internet", 30);
      $form->addText('usrip', 'IP zákazníka:', 30);
      if (!empty($usr)) $form["usrip"]->setOption('description', \Nette\Utils\Html::el('a')->href('http://'.$usr->usrip.'/')->setText('http://'.$usr->usrip.'/')->target('IP'));
    }
    $form->addSelect("usrsendtypid", "Vyúčtování posílat:", $invoices->getEnumSendType())
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label.');  
    
    $form['usrmail']
      ->addConditionOn($form["usrsendtypid"], Nette\Forms\Form::EQUAL, 1)
        ->addRule(Nette\Forms\Form::FILLED, 'Pro zasílání vyúčtování emailem je nutné vyplnit email.');  

    $form->addGroup("Fakturační údaje");
    $form->addText('usrirow1', 'Název firmy:', 50);
    $form->addText('usrirow2', 'Jméno:', 50);
    $form->addText('usrirow3', 'Ulice:', 50);
    $form->addText('usrirow4', 'Město + PSČ:', 50);
    $form->addText('usriic', 'IČ:', 20);
    $form->addText('usridic', 'DIČ:', 20);
    $form->addText('usripaydue', 'Splatnost (dny):', 20)
      ->setDefaultValue($this->config["INVOICE_DUE"]);
    
    $form->addText('usraccnumber', 'Číslo bankovního účtu:', 20)
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::PATTERN, 'Špatný formát účtu', '(\d{1,6}-)?\d{1,10}') 
      ->addConditionOn($form['usrpaytypid'], Nette\Forms\Form::EQUAL, 3)
        ->addRule(Nette\Forms\Form::FILLED, 'Pro platbu inkasem musí být vyplněno číslo bankovního účtu.');
    $form->addText('usracccode', 'Kód banky:', 20)
      ->addConditionOn($form['usrpaytypid'], Nette\Forms\Form::EQUAL, 3)
        ->addRule(Nette\Forms\Form::FILLED, 'Pro platbu inkasem musí být vyplněn kód banky.');
    
    $form->setCurrentGroup(Null);
    
    $form->addSelect("usrstatus", "Status", $usrs->getEnumUsrStatus());
    
    $form->addSubmit('save', 'Uložit')->getControlPrototype()->class('default');
    $form->onSuccess[] = array($this, 'userEditFormSubmitted');

    return $form;  
  }
  
  protected function createComponentUserNumbersEditForm() {
    $form = new Nette\Application\UI\Form(); 
    $pli = new \Model\PricelistsModel();
    
    $id = (int)$this->getParameter('id');
    
    $data = $form->addContainer('data');
    //pro novy zaznam
    $cont = $data->addContainer(0);  
    $cont->addText("numnumber", "Nové číslo:", 15)
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::INTEGER, 'Vyplňte přesně 13 číslic bez mezer (včetně mezinárodní volby 420)')
        ->addRule(Nette\Forms\Form::MAX_LENGTH, 'Vyplňte přesně 13 číslic bez mezer (včetně mezinárodní volby 420)', 13);

    $enumPliId = $pli->getEnumPliId();
    $cont->addSelect("numpliid", "Tarif", $enumPliId)
      ->setPrompt("Nevybrán ...")
      ->addConditionOn($cont["numnumber"], Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label.');
    
    $cont->addText("numdatefrom", "Od:", 12);  
    $cont->addText("numdateto", "Do:", 12);  
    
    //nactu stavajici cisla
    $numbers = dibi::fetchAll("SELECT * FROM numbers WHERE numusrid=%i", $id);
    foreach ($numbers as $row) {
      $cont = $data->addContainer($row->numid);  
      $cont->addText("numnumber", "Číslo:", 15)
        ->setDefaultValue($row->numnumber)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte telefonní číslo.')
        ->addCondition(Nette\Forms\Form::FILLED)
          ->addRule(Nette\Forms\Form::INTEGER, 'Vyplňte přesně 13 číslic bez mezer (včetně mezinárodní volby 420)')
          ->addRule(Nette\Forms\Form::MAX_LENGTH, 'Vyplňte přesně 13 číslic bez mezer (včetně mezinárodní volby 420)', 13);
      $cont->addSelect("numpliid", "Tarif", $enumPliId)
        ->setPrompt("Nevybrán ...")
        ->addConditionOn($cont["numnumber"], Nette\Forms\Form::FILLED)
          ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label.');  

      if (isset($enumPliId[$row->numpliid])) {
        $cont["numpliid"]->setDefaultValue($row->numpliid);
      }

      $cont->addText("numdatefrom", "Od:", 12)
        ->setDefaultValue($this->formatDateFromMySql($row->numdatefrom));  
      $cont->addText("numdateto", "Do:", 12)
        ->setDefaultValue($this->formatDateFromMySql($row->numdateto));  
    }
    
    $form->addSubmit('save', 'Uložit')->getControlPrototype()->class('default');
    $form->onSuccess[] = array($this, 'userNumbersEditFormSubmitted');
    return $form;  
  }
  
  protected function createComponentChangeTarifForm() {
    $form = new Nette\Application\UI\Form;
    $usrid = (int)$this->getParameter("id");
    $plis = new \Model\PricelistsModel();
    
    $form->addHidden('usrid', $usrid);
    
    $form->addSelect('pliid', 'Nový ceník:', $plis->getEnumPliId())
      ->setPrompt("Vyberte ... ")
      ->setRequired('Zadejte %label.');



    $numbers = dibi::query("SELECT numnumber FROM numbers WHERE numusrid=%i", $usrid)->fetchPairs("numnumber", "numnumber");  
    $form->addSelect('numnumber', 'Pro telefonní číslo:', $numbers)
      ->setPrompt("Vyberte ... ")
      ->setRequired('Zadejte %label.');

    $form->addCheckbox("dontSend", "Neposílat změnu na SimplyCom");

    $form->addSubmit('submit', 'Zapsat změnu ceníku');

    $form->onSuccess[] = [$this, 'changeTarifFormSubmitted'];
    return $form;
  }
  
  public function changeTarifFormSubmitted(Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      $values = $form->getValues();
      $plis = new \Model\PricelistsModel();
      $usrs = new UsersModel();
      if ($values->usrid > 0) {
        //ulozim do db
        $tacs = new \Model\TarifchangesModel();
        if ($tacs->insert(array(
          'tacpliid'=>$values->pliid,
          'tacusrid'=>$values->usrid,
          'tacnumber'=>$values->numnumber,
        ))) {
          $this->flashMessage("Požadavek byl úspěšně vložen.");

          //mailuji změnu
          $template = $this->createTemplate();
          $template->priceList = $plis->load($values->pliid);
          $template->user = $usrs->load($values->usrid);
          $template->phoneNumber = $values->numnumber;

          //service email podle ceníku
          $operatorMail = "";
          if (isset($this->configOperators[$template->priceList->pliserid])) {
            $operatorMail = $this->configOperators[$template->priceList->pliserid]["email"];
          }

          $template->setFile(WWW_DIR . '/../templates/mailNewTarif.phtml');

          try {
            $this->sendMail($this->config["SERVER_MAIL"], 'Telcall - požadavek na změnu tarifu z administrace', $template);
            $this->sendMail('<EMAIL>', 'Telcall - požadavek na změnu tarifu z administrace', $template);

            if ($operatorMail !== "" && $values->dontSend !== TRUE) {
              $this->sendMail($operatorMail, 'Telcall - požadavek na změnu tarifu', $template);
            }

            $this->flashMessage("Děkujeme Váš požadavek byl odeslán. O změně Vás budeme informovat.");

          } catch (\Exception $e) {
            $form->addError("Odeslání požadavku se nezdařilo.");
          }

        }
      }  
    }
    $this->redirect('User:edit', $values->usrid);  
  }
  
  
  protected function createComponentSearchForm() {
    $invs = new InvoicesModel();

    $form = new Nette\Application\UI\Form();
    $form->addText("name", "Jméno", 10)
      ->setDefaultValue($this->sName);

    $form->addSelect("paytypid", "Způsob platby", $invs->getEnumPayTypId())
      ->setPrompt("")
      ->setDefaultValue($this->sPayTypId);

    $form->addText("phone", "Telefon", 10)
      ->setDefaultValue($this->sPhone);

    $form->addCheckbox("h", "Jen blokované", 10)
      ->setDefaultValue($this->sH);

    $form->addCheckbox("np", "Včetně neuhrazených", 10)
      ->setDefaultValue($this->sNp);

    $form->addSubmit('search', 'Vyhledat');
    $form->addSubmit('clear', 'Vymazat filtr');
    $form->onSuccess[] = array($this, 'searchFormSubmitted');
    return $form;
  }
  
  public function searchFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      if ($form["clear"]->isSubmittedBy()) { 
        $this->sName = Null;
        $this->sPayTypId = Null;
        $this->sPhone = Null;
        $this->sH = 0;
        $this->sNp = 0;
      } else {
        $vals = $form->getValues();
        $this->sName = $vals["name"];
        $this->sPayTypId = $vals["paytypid"];
        $this->sPhone = $vals["phone"];
        $this->sH = (bool)$vals["h"];
        $this->sNp = (bool)$vals["np"];
      }
    }
    $this->redirect("User:default");  
  }
  
  public function actionBatchAction() {
    if (isset($_POST['maillist'])) {
      $ids = $_POST['usrid'];
      $this->redirect('maillist', array('ids'=>$ids));
    }

    if (isset($_POST['print'])) {
      $ids = $_POST['usrid'];
      $this->redirect('print', array('ids'=>$ids));
    }

  }
  
  public function actionMaillist(array $ids) {
    $idSql = trim(implode(',', $ids), ',');
    $mails = dibi::query("SELECT usrid, usrmail FROM users WHERE usrid IN ($idSql) GROUP BY usrmail")->fetchPairs('usrid', 'usrmail');  
    $this->template->mails = $mails;
    $mailsStr = trim(implode('|', $mails), '|');
    $form = $this['maillistForm'];
    if (!$form->isSubmitted()) {
      $form->setDefaults(array(
        'mailsList'=>$mailsStr,
      ));  
    }
  }

  public function actionPrint(array $ids) {
    $idSql = trim(implode(',', $ids), ',');
    $this->template->users = dibi::fetchAll("SELECT * FROM users WHERE usrid IN ($idSql) order by usrname");
    $this->template->notPayedByClient = dibi::query("SELECT * FROM invoices WHERE invstatus=0 order by invpaydate")->fetchAssoc("invusrid,invid");

    $invs = new \Model\InvoicesModel();
    $this->template->enum_usrpaytypid = $invs->getEnumPayTypId();
  }

  protected function createComponentMaillistForm() {
    $form = new Nette\Application\UI\Form();
    $form->addHidden("mailsList");
    $form->addText("subject", "Předmět", 80)
      ->setRequired('Zadejte %label.');
    $form->addTextArea("text", "Text", 60, 6)
      ->setRequired('Zadejte %label.');
      
    $form->addUpload("attachment", "Příloha")
      ->addCondition(Nette\Application\UI\Form::FILLED)
        ->addRule(Nette\Application\UI\Form::MAX_FILE_SIZE,"Maximální velikost souboru může být 3MB",3145728);  
    $form->addSubmit('send', 'Odeslat');
    $form->onSuccess[] = array($this, 'maillistFormSubmitted');
    return $form;
  }
  
  public function maillistFormSubmitted (Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      $vals = $form->getValues();
      $mails = explode('|', $vals->mailsList);
      $body = nl2br($vals->text);
      
      $mail = new Nette\Mail\Message();
      $mail->setFrom($this->config["SERVER_NAME"].' <'.$this->config["SERVER_MAIL"].'>');
      $mail->setSubject($vals->subject." - ".$this->config["SERVER_NAME"]);
      $mail->setHtmlBody($body);
      if ($vals->attachment->isOk()) {
        $file = $vals->attachment;
        $fileTmpName = (string)$file->getTemporaryFile();
        $fileName = (string)$file->getName();
        $fileContent = (string)file_get_contents($fileTmpName, False); 
        $mail->addAttachment($fileName, $fileContent);  
      }     
      foreach ($mails as $mailAddress) {
        try {
          $mail->setHeader('To', null);  
          $mail->addTo(trim($mailAddress));
          try {
            $this->mailer->send($mail);
          } catch (InvalidStateException $e) {
            $this->flashMessage("chyba pri odeslani mail ".$mailAddress."(".$e.")", "err");
          }
        } catch (InvalidArgumentException $e) {
          $this->flashMessage("mail neni validni ".$mailAddress."(".$e.")");
        }
      }
      $this->flashMessage("Emaily byly odeslány.");   
    }
    $this->redirect("User:default");  
  }
  
  protected function createComponentUserContractsEditForm() {
    $form = new Nette\Application\UI\Form(); 
    $cons = new \Model\ContractsModel();
    
    $id = (int)$this->getParam('id');  
    
    /*
    //pro novy zaznam
    $cont = $form->addContainer("add");  
    $cont->addSelect("colconid", "Nová smlouva:", $cons->getEnumConMasId())
      ->setPrompt('Vyberte ...')
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label.');
    */
    
    $data = $form->addContainer('data');
    //nactu stavajici smlouvy a dodatky co nejsou podepsane
    $rows = dibi::fetchAll("
      SELECT * FROM contracts_log
      INNER JOIN contracts ON (conid=colconid) 
      WHERE coldatesigned IS NULL AND colusrid=%i", $id);
    foreach ($rows as $row) {
      $cont = $data->addContainer($row->colid);  
      $cont->addText("coldatesigned", "Datum podpisu:", 15)
        ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label.');
    }
    
    $form->addSubmit('save', 'Uložit')->getControlPrototype()->class('default');
    $form->onSuccess[] = array($this, 'userContractsEditFormSubmitted');
    return $form;  
  }
}  
?>
