<?php
namespace AdminModule;
use dibi;
use Nette;

final class ConfigPresenter extends BasePresenter {

  public function formConfigSubmitted(Nette\Application\UI\Form $form) {
    if ($form['save']->isSubmittedBy()) {
      $config = new \Model\ConfigModel();
      $values = $form->getValues();
      foreach ($values as $key => $value) {
        $arr = explode('_', $key);
        if ($arr[0]==='id') {
          $config->update($arr[1], array('cfgvalue'=>$value));
        }    
      }
      //vymazu cache - TODO: implement proper cache clearing for Nette 3.x
      // $cache = \Nette\Environment::getCache('app');
      // unset($cache['config']);
      $this->flashMessage('Uloženo v pořádku');
    }
    $this->redirect('default');
  }
  
  /********************* view default *********************/ 
  
  public function renderDefault() {
           
  }
  
  /********************* facilities *********************/

  /**
   * Component factory.
   * @param  string  component name
   * @return void
   */
  protected function createComponentConfigForm() {
    $config = new \Model\ConfigModel();
    $form = new Nette\Application\UI\Form;
    
    // setup custom rendering
    $renderer = $form->getRenderer();
    $renderer->wrappers['label']['container'] = 'td';
    $renderer->wrappers['control']['container'] = 'th';
     
    $rows = dibi::fetchAll('SELECT * FROM config ORDER BY cfgorder');
    
    foreach ($rows as $row) {  
      switch ($row->cfgcontroltype) {
         case 'text':
           $form->addText('id_'.$row->cfgid, $row->cfgnote, 40, $config->getColProperty('cfgvalue', 'size'))
             ->setDefaultValue($row->cfgvalue);
           break;
         case 'combo':
           list($ids, $texts) = explode(';', $row->cfgvalues);
           $idsarr = explode(',', $ids); 
           $textsarr = explode(',', $texts);
           if (count($idsarr) == 0 || count($textsarr) == 0 || count($idsarr) != count($textsarr)) continue;
           $vals = array_combine($idsarr, $textsarr);
           $form->addSelect('id_'.$row->cfgid, $row->cfgnote, $vals)
             ->setDefaultValue($row->cfgvalue);
           break;
           break;
         case 2:
      
           break;
      }
    }
    $form->addSubmit('save', 'Uložit');
    $form->onSuccess[] = array($this, 'formConfigSubmitted');
    return $form;
  }
}  
?>