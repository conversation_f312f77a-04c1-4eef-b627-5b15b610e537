<?php
namespace FrontModule;
use dibi;
use Nette;

abstract class BasePresenter extends \BasePresenter {
  const NS_LOGIN = 'user';
  
  protected $user = Null;
  protected $userData = Null;

  protected function startup() {
    parent::startup();
    
    //nactu uzivatele
    $this->user = $this->getUser();
    $this->user->getStorage()->setNamespace(self::NS_LOGIN);

    if ($this->user->isLoggedIn()) {
      $user = new \Model\UsersModel();
      $this->userData = $user->load($this->user->id);
    }
    if ($this->userData) {

    } else {
      $this->userData = new \Dibi\Row(array('usrid'=>0));
      $this->user->logout();
    }
    
  }  
  
  protected function beforeRender() {
    $this->template->identity = $this->user;
    $this->template->user = $this->userData;  
    parent::beforeRender();
  }
  
  public function createComponentTexylaJs() {
    $files = new \WebLoader\FileCollection(WWW_DIR . '/texyla');
    $files->addFiles(array(
      // core
      "js/texyla.js",
      "js/selection.js",
      "js/texy.js",
      "js/buttons.js",
      "js/dom.js",
      "js/view.js",
      "js/ajaxupload.js",
      "js/window.js",

      // languages
      "languages/cs.js",

      // plugins
      "plugins/keys/keys.js",
      "plugins/resizableTextarea/resizableTextarea.js",
      "plugins/img/img.js",
      "plugins/table/table.js",
      "plugins/link/link.js",
      "plugins/emoticon/emoticon.js",
      "plugins/symbol/symbol.js",
      "plugins/files/files.js",
      "plugins/color/color.js",
      "plugins/textTransform/textTransform.js",
      "plugins/youtube/youtube.js",
      "plugins/gravatar/gravatar.js",
    
      //init file
      WWW_DIR . "/js/texyla-init.js"
    ));
    
    $compiler = \WebLoader\Compiler::createJsCompiler($files, WWW_DIR . '/webtemp');
    
    $filter = new \WebLoader\Filter\VariablesFilter(array(
      "baseUri" => $this->getHttpRequest()->url->baseUrl,
      "previewPath" => $this->link("Texyla:preview"),
      "filesPath" => $this->link("Texyla:listFiles"),
      "filesUploadPath" => $this->link("Texyla:upload"),
      "filesMkDirPath" => $this->link("Texyla:mkDir"),
      "filesRenamePath" => $this->link("Texyla:rename"),
      "filesDeletePath" => $this->link("Texyla:delete"),
    ));
    $compiler->addFilter($filter);

    return new \WebLoader\Nette\JavaScriptLoader($compiler, $this->template->basePath . '/webtemp'); 
  }
}
