<?php
namespace FrontModule;
use dibi;
use Nette;

class UserPresenter extends SecuredPresenter {

	public function actionDefault() {
    //pokud neni prihlaseny poslu ho na prihlasovaci okno
    if (!$this->user->isLoggedIn()) {
      $this->flashMessage("Na tuto stránku mají př<PERSON>tup jen přihlášení uživatelé");
      $this->redirect('logIn'); 
    } 
    //cenik
    $pl = new \Model\PricelistsModel();
    $numbers = dibi::fetchAll("SELECT * FROM numbers WHERE numusrid=%i", $this->userData->usrid); 
    //nactu ceniky pro jednotliva cisla
    foreach ($numbers as $key => $row) {
      $numbers[$key]->pricelist = $pl->load($row->numpliid);       
    }
    $this->template->numbers = $numbers;
  }

  public function renderEdit() {
    //$this->redirect("default");
    $id = 0;
    if ($this->user->isLoggedIn()) {
      $id = $this->user->getIdentity()->id;
    }
    $form = $this->getComponent("editForm");
    
    if (!$form->isSubmitted()) {
      $users = new \Model\UsersModel();
      $user = $users->load($id);
      if (!$users) {
        throw new Nette\Application\BadRequestException('Záznam nenalezen (id='.$id.')');
      }
      $form->setDefaults($user);
    }  
  }
  
  public function renderEditNumber($id) {
    if ($this->user->isLoggedIn()) {
      $usrid = $this->user->getIdentity()->id;
    } else {
      //neni nalogovan pryc
      $this->flashMessage("Nejste přihlášen.", "err");
      $this->redirect('default');
    }
    
    //kontrola zda edituje svoje cislo
    $cnt = (int)dibi::fetchSingle("SELECT COUNT(*) FROM numbers WHERE numusrid=%i", $usrid, " AND numid=%i", $id);
    if ($cnt == 0) {
      $this->flashMessage("Nejste přihlášen nebo se snažíte editovat číslo které vám nepatří.", "err");
      $this->redirect('default');
    }
    $form = $this->getComponent("editNumberForm");
    
    if (!$form->isSubmitted()) {
      $nums = new \Model\NumbersModel();
      $num = $nums->load($id);
      if (!$num) {
        throw new Nette\Application\BadRequestException('Záznam nenalezen (id='.$id.')');
      }
      $form->setDefaults($num);
      $this->template->number = $num;
    }  
  }
  
  public function renderCalls() {
    $calls = new \Model\CallsModel();
    $num = $this->getParameter('num');
    $mon = (int)$this->getParameter('mon');
    $yea = (int)$this->getParameter('yea');
    //nactu cisla uzivatele
    $numbers = dibi::query("SELECT numnumber FROM numbers WHERE numusrid=%i", $this->userData->usrid)->fetchPairs('numnumber', 'numnumber');
    $this->template->numbers = $numbers;
    $numbersSql = implode(',', $numbers);
    if (!empty($num)) {
      if (!array_key_exists($num, $numbers)) { 
        $this->flashMessage("Číslo $num není přiřazeno k Vašemu účtu.", 'err');
        $num = Null;
        $this->redirect('calls');
      }  
      $this->template->curNumber = $num;
      $this->template->lists = dibi::query("SELECT calnumber, calmonth, calyear FROM calls WHERE calnumber=%s", $num)->fetchAssoc("calyear,calmonth");
      $this->template->enu_months = $calls->getEnumMonId(); 
      if ($mon > 0 && $yea > 0) {
        $this->template->curMonth = $mon;
        $this->template->curYear = $yea;
        $this->template->calls = dibi::fetchAll("SELECT * FROM calls WHERE calnumber=%s", $num, " AND calmonth=%i ", $mon, " AND calyear=%i", $yea, "ORDER BY caldatetime");
      }  
    }
  }
  
  public function renderInvoices() {
    $invoices = dibi::fetchAll("SELECT * FROM invoices WHERE invusrid=%i", $this->userData->usrid, " ORDER BY invyear, invmonth");
    $this->template->invoices = $invoices;
  }
  
  public function renderNumberitems($month, $year) {
    //nactu cisla prihlaseneho uzivatel
    $numbers = dibi::fetchAll("SELECT numnumber FROM numbers WHERE numusrid=%i", $this->userData->usrid);
    foreach ($numbers as $number) {
      $number->items = dibi::fetchAll("SELECT * FROM numberitems WHERE nninumber=%s", $number->numnumber, " AND nnimonth=%i", $month, " AND nniyear=%i", $year, " ORDER BY nniid");  
    }
    $this->template->numbers = $numbers;
    $this->template->period = "$month/$year";
  }
  
  
  
  public function actionGetInvoice($id, $dest="I") {
    //vraci template prislusne
    //$template = $this->getTemplate();
    $invoices = New \Model\InvoicesModel();
    $invoice = dibi::fetch("SELECT * FROM invoices WHERE invid=%i", $id, " AND invusrid=%i", $this->userData->usrid);
    $this->getInvoice($invoice, $dest);
  }
  
  public function actionGetContract($dest="I") {
    //vraci template prislusne
    $this->getContract($this->userData->usrid, $dest);
  }
    
  public function actionLogOut() {
    $this->user->logout();
    $this->flashMessage('Byl/a jste odhlášen.', 'ok');
    $this->redirect('Homepage:default');
  }
  
  public function actionVerify($id, $code) {
    if ($this->verify($id, $code)) {
      $this->flashMessage("Váš účet byl úspěšně ověřen", "ok");  
    } else {
      $this->flashMessage("Ověření se nezdařilo. Zkontrolujte, zda jste ověřovací kód opsal/a správně.", 'err');
    }
    if ($this->user->isLoggedIn()) {
      $this->redirect('default');
    } else {
      $this->redirect('logIn');
    }  
  }
  
  public function actionLogIn() {
    if ($this->user->isLoggedIn()) $this->redirect('User:default');
    $this->backinkSave = false;
  }
  
  protected function createComponentLogInForm() {
		$form = new Nette\Application\UI\Form;
		$form->addText('usrphone', 'Telefonní číslo:')
      ->addRule(Nette\Forms\Form::INTEGER, 'Vyplňte přesně 9 číslic bez mezer')
      ->addRule(Nette\Forms\Form::LENGTH, 'Vyplňte přesně 9 číslic bez mezer', 9)
			->setRequired('Zadejte Vaše telefonní číslo jako přihlašovací jméno.');

		$form->addPassword('usrpassword', 'Heslo:')
			->setRequired('Zadejte prosím heslo.');

		$form->addSubmit('submit', 'Přihlásit se');

		$form->onSuccess[] = [$this, 'logInFormSubmitted'];
		return $form;
	}
  
	public function logInFormSubmitted(Nette\Application\UI\Form $form) {
		try {
			$values = $form->getValues();
			$this->getUser()->setExpiration('+ 40 minutes', TRUE);
			$this->getUser()->login((string)$values->usrphone, (string)$values->usrpassword);
			if (!empty($this->appSession->backlink)) {
        $this->restoreRequest($this->appSession->backlink);
      } else {
        $this->redirect('User:default'); 
      }
		} catch (Nette\Security\AuthenticationException $e) {
			$form->addError("Přihlášení se nezdařilo. ".$e->getMessage());
		}
	}

  protected function createComponentEditForm() {
    $id = $this->userData->usrid;
    $form = new Nette\Application\UI\Form;
    
    $pricelists = new \Model\PricelistsModel();
    $invoices = new \Model\InvoicesModel();
    
    /*
    $form->addText('usrname', 'Jméno, příjmení:', 30)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte jméno, příjmení.');
    
    $form->addText('usrmail', 'Email:', 30)
      ->setEmptyValue('@')
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte email.')
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::EMAIL, 'Email nemá správný formát');  
    
    $form->addText('usrphone', 'Telefon:', 30)
      ->setOption('description', ' slouží klientovi zároveň jako přihlašovací jméno')
      ->addRule(Nette\Forms\Form::INTEGER, 'Vyplňte přesně 9 číslic bez mezer')
      ->addRule(Nette\Forms\Form::LENGTH, 'Vyplňte přesně 9 číslic bez mezer', 9)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte telefon.');
    
    $form->addText('usrirow1', 'Název firmy:', 50);
    $form->addText('usrirow2', 'Jméno:', 50);
    $form->addText('usrirow3', 'Ulice:', 50);
    $form->addText('usrirow4', 'Město + PSČ:', 50);
    $form->addText('usriic', 'IČ:', 20);
    $form->addText('usridic', 'DIČ:', 20);
    
    */
    
    $form->addText('usrname', 'Jméno, příjmení:', 30)
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte jméno, příjmení.');
    
    $form->addText('usrmail', 'Email:', 30)
      ->setEmptyValue('@')
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte email.')
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::EMAIL, 'Email nemá správný formát');  
    
    /*
    $form->addSelect("usrpliid", "Ceník volání", $pricelists->getEnumPliId())
      ->setPrompt("Vyberte ...");
    */

    $form->addSelect("usrpaytypid", "Způsob úhrady", $invoices->getEnumPayTypId())
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte způsob úhrady.');  
    
    $form->addText('usraccnumber', 'Číslo bankovního účtu:', 20)
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::PATTERN, 'Špatný formát účtu', '(\d{1,6}-)?\d{1,10}') 
      ->addConditionOn($form['usrpaytypid'], Nette\Forms\Form::EQUAL, 3)
        ->addRule(Nette\Forms\Form::FILLED, 'Pro platbu inkasem musí být vyplněno číslo bankovního účtu.');
    $form->addText('usracccode', 'Kód banky:', 20)
      ->addConditionOn($form['usrpaytypid'], Nette\Forms\Form::EQUAL, 3)
        ->addRule(Nette\Forms\Form::FILLED, 'Pro platbu inkasem musí být vyplněn kód banky.');
    
    
    $form->addSelect("usrsendtypid", "Vyúčtování posílat:", $invoices->getEnumSendType())
      ->addRule(Nette\Forms\Form::FILLED, 'Prosím vyplňte %label.'); 
    
     
    $form->addPassword('usrpasswordold', 'Původní heslo:')
      ->addCondition(Nette\Forms\Form::FILLED)
        ->addRule(Nette\Forms\Form::MIN_LENGTH, 'Heslo musí mít alespoň %d znaků', 6);
    
    $form->addPassword('usrpassword', 'Nové heslo:')
      ->addConditionOn($form["usrpasswordold"], Nette\Application\UI\Form::FILLED)
        ->addRule(Nette\Forms\Form::MIN_LENGTH, 'Nové heslo musí mít alespoň %d znaků', 6)
        ->addRule(Nette\Forms\Form::FILLED, 'Zadejte prosím nové heslo.');
          
    $form->addPassword('usrpassword2', 'Nové heslo podruhé:')
      ->addConditionOn($form["usrpassword"], Nette\Application\UI\Form::FILLED)
        ->addRule(Nette\Forms\Form::MIN_LENGTH, 'Nové heslo podruhé musí mít alespoň %d znaků', 6)
        ->addRule(Nette\Application\UI\Form::FILLED, 'Zadejte prosím nové heslo podruhé.')
        ->addRule(Nette\Forms\Form::EQUAL, 'Hesla se neshodují', $form['usrpassword']);      
   
    $form->addSubmit('save', ($id > 0 ? 'Uložit změny' : 'Registrovat se'));

    $form->onSuccess[] = [$this, 'userEditFormSubmitted'];
    return $form;
  }
  
  protected function createComponentEditNumberForm() {
    $id = $this->userData->usrid;
    $form = new Nette\Application\UI\Form; 
    $form->addtext('numpin1', 'PIN 1:');
    $form->addtext('numpin2', 'PIN 2:');
    $form->addtext('numpuk1', 'PUK 1:');
    $form->addtext('numpuk2', 'PUK 2:');
    $form->addtext('numbpuk', 'BPUK:');
   
    $form->addSubmit('save', 'Uložit změny');

    $form->onSuccess[] = [$this, 'editNumberFormSubmitted'];
    return $form;
  }
  
  public function editNumberFormSubmitted(Nette\Application\UI\Form $form) {
    try {
      $values = $form->getValues();
      $numid = $this->getParameter("id");
      $values->numusrid = $this->userData->usrid;
      $nums = new \Model\NumbersModel();
      $nums->update($numid, $values);
      
    } catch (Nette\Security\AuthenticationException $e) {
      $form->addError("Uložení se nezdařilo. ".$e->getMessage());
    }
    if (!$form->hasErrors()) {
      $this->redirect("default");
    }
  }
  
  public function userEditFormSubmitted(Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      $frmvals = $form->getValues();
      $id = $this->userData->usrid;
      $users = new \Model\UsersModel();
            
      if ($id > 0) {
         $user = $users->load($id);
         $passwordChanged = false; 
         $emailChanged = false;
        //zmenil heslo?
        if (!empty($frmvals["usrpassword"])) {
          //kontrola s puvodnim heslem
          if (\Model\BaseModel::calculateHash($frmvals["usrpasswordold"]) == $user->usrpassword) {
            $frmvals["usrpassword"] = \Model\BaseModel::calculateHash($frmvals["usrpassword"]);
            $passwordChanged = true; 
          } else {
            $form->addError("Původní heslo není zadáno správně, není možné udělat změnu hesla.");
            unset($frmvals["usrpassword"]);
          }
        } else {
          unset($frmvals["usrpassword"]);
        }
        
        //uklidim promenne ktere nejsou v objektu user
        unset($frmvals["usrpasswordold"]);
        unset($frmvals["usrpassword2"]);
        if (!$form->hasErrors()) {
          if ($users->update($id, $frmvals)) $this->flashMessage('Změny uloženy.'); 
          //$this->flashMessage('Vaše údaje byly aktualizovány.');
          if ($passwordChanged) $this->flashMessage('Heslo bylo změněno.');  
        }
        if (!$form->hasErrors()) {
          $this->redirect('edit');
        } else {
          return false;
        }  
      } else {
        //novy zazanam
        //ulozim si do sablony hesle nez se zaheshuje
        $template = $this->createTemplate();
        $template->usrpassword = $frmvals["usrpassword"];
        
        //uklidim promenne ktere nejsou v objektu user
        unset($frmvals["usrpassword2"]);
        unset($frmvals["agree"]);
        unset($frmvals["antispam"]);
        
        $usrpassw = $frmvals["usrpassword"];
        $frmvals["usrpassword"] = \Model\BaseModel::calculateHash($frmvals["usrpassword"]);
        $frmvals["usrmailverify"] = \Nette\Utils\Strings::lower(substr(BaseModel::calculateHash($frmvals["usrmail"].$frmvals["usrpassword"]),0, 4)); 
        $frmvals["usrmailverified"] = 1;
        
        //ulozim novou registraci
        $id = $users->insert($frmvals);
        
        if ($id > 0) {
          
          //naplnim row
          $user = $users->load($id);
          $template->user = $user;
          $template->setFile(Nette\Environment::expand('%wwwDir%/../templates/mailUserAdd.phtml'));
          //odeslu email
          $this->sendMail($user->usrmail, 'Registrace', $template);
          
          //prihlasim
          $this->user->setExpiration('+ 20 minutes', TRUE);
          $this->user->login((string)$form['usrmail']->getValue(), (string)$form['usrpassword']->getValue());
          
        }  
        $this->flashMessage('Vaše registrace byla přijata a nyní jste přihlášen/a na svůj účet. Na Váš email jsme Vam poslali ověřovací kód, pomocí něj aktivujete Váš účet.', 'ok');
        $this->redirect('edit');
      }
    }
  }

  
  protected function createComponentNewPasswordForm() {
    $form = new Nette\Application\UI\Form;
   $form->addText('usrphone', 'Telefonní číslo:')
      ->addRule(Nette\Forms\Form::INTEGER, 'Vyplňte přesně 9 číslic bez mezer')
      ->addRule(Nette\Forms\Form::LENGTH, 'Vyplňte přesně 9 číslic bez mezer', 9)
      ->setRequired('Zadejte Vaše telefonní číslo jako přihlašovací jméno.');
      
    $form->addSubmit('submit', 'Zaslat nové heslo');

    $form->onSuccess[] = [$this, 'newPasswordFormSubmitted'];
    return $form;
  }
  
  public function newPasswordFormSubmitted(Nette\Application\UI\Form $form) {
    if ($form->isSubmitted()) {
      $formVals = $form->getValues();
      //najdu prislusneho uzivatele
      $user = dibi::fetch("SELECT * FROM users WHERE usrtype='call' AND usrphone=%s", $formVals["usrphone"]);
      if ($user) {
        $newPassw = \Model\BaseModel::genPassword();
        $saveVals["usrpassword"] = \Model\BaseModel::calculateHash($newPassw);
        $users = new \Model\UsersModel();
        if ($users->update($user->usrid, $saveVals)) {
          //odmailuju na prislusny email
          $template = $this->createTemplate();
          $template->user = $user;
          $template->usrpassword = $newPassw;
          $template->setFile(Nette\Environment::expand('%wwwDir%/../templates/mailNewPassword.phtml'));
          $this->sendMail($user->usrmail, "Nové heslo", $template);  
        } else {
          $form->addError("Změna hesla se nepodařila.");         
        }
        $this->flashMessage("Heslo bylo zasláno na Váš email.", "ok");
        $this->redirect('logIn');  
      } else {
        $form->addError("Účet s tímto telefonním číslem neexistuje.");
      } 
    }
  }
}
