<?php
use Nette\Application\Routers\Route;

// Load Composer autoloader
require __DIR__ . '/../vendor/autoload.php';

// Configure application
$configurator = new Nette\Bootstrap\Configurator;

// Enable Nette Debugger for error visualisation & logging
// Detect development environment based on server name and environment
$isDevelopment = false;
$serverName = $_SERVER['SERVER_NAME'] ?? $_SERVER['HTTP_HOST'] ?? 'localhost';

// Development environments
$devServers = [
    'localhost',
    '127.0.0.1',
    'telcallcz.local',
    'web.telcallcz.orb.local',
    'dev.telcallcz.cz',
    'test.telcallcz.cz'
];

// Check if current server is development
foreach ($devServers as $devServer) {
    if (strpos($serverName, $devServer) !== false) {
        $isDevelopment = true;
        break;
    }
}

// Check for explicit development environment variable
if (getenv('APP_ENV') === 'development' || getenv('NETTE_DEBUG') === '1') {
    $isDevelopment = true;
}

// For Docker: only enable debug if explicitly requested
if (file_exists(__DIR__ . '/../.docker') && !$isDevelopment) {
    // In Docker, only enable debug for known dev servers or explicit env var
    $isDevelopment = false;
}

$configurator->setDebugMode($isDevelopment);
$configurator->enableDebugger(__DIR__ . '/../log', '<EMAIL>');

// Suppress deprecated warnings in Tracy (only in development)
if ($isDevelopment) {
    Tracy\Debugger::$logSeverity = E_ERROR | E_WARNING | E_PARSE | E_NOTICE;
}

// Enable RobotLoader - this will load all classes automatically
$configurator->setTempDirectory(__DIR__ . '/../temp');
$configurator->createRobotLoader()
  ->addDirectory(APP_DIR)
  ->addDirectory(LIBS_DIR)
  ->register();

// Create Dependency Injection container from config.neon file
$configurator->addConfig(__DIR__ . '/config/config.neon');

// Set environment - for Docker development
$configurator->addStaticParameters(['env' => 'development']);

$container = $configurator->createContainer();

// dibi - set default connection for backward compatibility
$connection = $container->getService("connection");
\dibi::setConnection($connection);

// Route flags are now set per route in Nette 3.x

//Setup application router
$router = new Nette\Application\Routers\RouteList;

$router->addRoute('index.php', 'Front:Homepage:default', Route::ONE_WAY);
$router->addRoute('administrace/<presenter>/<action>/[<id>]', array(
    'module' => 'Admin',
    'presenter' => 'Admin',
    'action' => 'default',
));

$router->addRoute('kategorie-<key>/', array(
    'module' => 'Front',
    'presenter' => 'Catalog',
    'action' => 'category',
    'key' => NULL,
));

$router->addRoute('kategorie-<key>/<key2>/', array(
    'module' => 'Front',
    'presenter' => 'Catalog',
    'action' => 'subCategory',
    'key' => NULL,
    'key2' => NULL
));

$router->addRoute('diskuze-<key>/<id [0-9]+>/', array(
    'module' => 'Front',
    'presenter' => 'Forum',
    'action' => 'detail',
    'key' => NULL
));

$router->addRoute('text-<key>', array(
    'module' => 'Front',
    'presenter' => 'Page',
    'action' => 'detail',
    'key' => NULL
));

$router->addRoute('<presenter>/<action>/<id>', array(
    'module' => 'Front',
    'presenter' => 'Homepage',
    'action' => 'default',
    'id' => NULL,
));

// In Nette 3.x, router should be configured in config.neon or via DI
// For now, we'll add it manually but this should be moved to config
$container->addService('router', $router);

// Configure and run the application!
$application = $container->getByType(Nette\Application\Application::class);
$application->run();