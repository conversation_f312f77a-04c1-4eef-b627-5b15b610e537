<?php

/**
 * Users and <PERSON><PERSON> authenticator.
 */
class Authenticator implements Nette\Security\Authenticator {
  use Nette\SmartObject;
  /** namespace admin */
  const NS_ADMIN = 'admin';
  /** namespace user */
  const NS_USER  = 'user';
  
  /**
   * Performs an authentication
   * @param  string $username
   * @param  string $password
   * @return Nette\Security\IIdentity
   * @throws Nette\Security\AuthenticationException
   */
  public function authenticate(string $username, string $password, string $namespace = ''): Nette\Security\IIdentity {

    // Determine namespace based on username format
    // If username contains @ it's admin, otherwise it's user
    if (empty($namespace)) {
	    $namespace = (strpos($username, '@') !== false) ? self::NS_ADMIN : self::NS_USER;
    }
    if (empty($namespace)) throw new Nette\Security\AuthenticationException("Špatné volání authentizace.", Nette\Security\Authenticator::IDENTITY_NOT_FOUND);

    switch ($namespace) {
       case self::NS_USER:
         $sql = "SELECT usrid AS id, usrmail AS mail, usrpassword AS password, usrstatus AS status FROM users WHERE usrtype='call' AND usrphone=%s";
         $role = Null; //neni treba
         break;
       case self::NS_ADMIN:
         $sql = "SELECT admid AS id, admmail AS mail, admpassword AS password, admstatus AS status FROM admins WHERE admmail=%s";
         $role = 'admin'; //zatim natvrdo
         break;
    }
    $row = dibi::fetch($sql, $username);

    if (!$row) {
      throw new Nette\Security\AuthenticationException("Užívatel s přihlašovacím jméném '$username' ($namespace) nenalezen.", Nette\Security\Authenticator::IDENTITY_NOT_FOUND);
    }

    if ($row->password !== \Model\BaseModel::calculateHash($password)) {
      throw new Nette\Security\AuthenticationException("Špatné heslo.", Nette\Security\Authenticator::INVALID_CREDENTIAL);
    }

    unset($row->password);
    return new Nette\Security\Identity($row->id, $role, $row->toArray());
  }
}
