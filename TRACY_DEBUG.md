# Tracy Debug Bar - Konfigurace pro Development/Production

## Automatick<PERSON> detekce prostředí

Tracy debug bar se automaticky aktivuje pouze v development prostředí podle následujících pravidel:

### 🟢 Development prostředí (Tracy bar AKTIVNÍ)

Tracy bar se zobrazí, pokud je splněna **JAKÁKOLI** z těchto podmínek:

1. **Server name obsahuje development domény:**
   - `localhost`
   - `127.0.0.1`
   - `telcallcz.local`
   - `web.telcallcz.orb.local`
   - `dev.telcallcz.cz`
   - `test.telcallcz.cz`

2. **Environment variables:**
   - `APP_ENV=development`
   - `NETTE_DEBUG=1`

### 🔴 Production prostředí (Tracy bar NEAKTIVNÍ)

Tracy bar se **NEZOBRAZÍ** pro všechny ostatní domény:
- `www.telcallcz.cz`
- `telcallcz.cz`
- `production.example.com`
- atd.

## Docker konfigurace

### Development (aktuální nastavení)
```yaml
environment:
  APP_ENV: development
  NETTE_DEBUG: '1'
```

### Production
```yaml
environment:
  APP_ENV: production
  # NETTE_DEBUG neuvádět nebo nastavit na '0'
```

## Manuální přepnutí

### Zapnout Tracy bar (development)
```bash
# V docker-compose.yml
environment:
  APP_ENV: development
  NETTE_DEBUG: '1'
```

### Vypnout Tracy bar (production)
```bash
# V docker-compose.yml
environment:
  APP_ENV: production
  # Odstraň nebo zakomentuj NETTE_DEBUG
```

## Testování

```bash
# Development (Tracy bar se zobrazí)
curl -s http://localhost/ | grep "Tracy Debug Bar"

# Production simulation (Tracy bar se nezobrazí)
curl -s -H "Host: www.telcallcz.cz" http://localhost/ | grep "Tracy Debug Bar"
```

## Bezpečnost

- ✅ Tracy bar se **automaticky vypne** v produkci
- ✅ **Žádné deprecated hlášky** v produkci
- ✅ **Environment variables** mají nejvyšší prioritu
- ✅ **Server name detection** jako fallback

## Poznámky

- Tracy bar obsahuje citlivé informace (databázové dotazy, session data, atd.)
- **NIKDY** nenechávejte Tracy bar aktivní v produkci
- Environment variable `APP_ENV=development` má nejvyšší prioritu
