FROM php:8.1-apache

# Instalace pot<PERSON>ebn<PERSON><PERSON> balíčků pro PHP 8.1
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        curl \
        libzip-dev \
        libwebp-dev \
        libicu-dev \
        build-essential \
        libssl-dev \
        zlib1g-dev \
        libpng-dev \
        libjpeg-dev \
        libfreetype6-dev \
        ssl-cert && \
    a2enmod rewrite ssl && \
    a2ensite default-ssl && \
    docker-php-ext-configure gd --with-freetype --with-jpeg --with-webp && \
    docker-php-ext-install mysqli pdo pdo_mysql opcache zip intl gd && \
    docker-php-ext-enable pdo_mysql opcache && \
    rm -rf /var/lib/apt/lists/*

# Instalace Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Nastavení PHP konfigura<PERSON>n<PERSON><PERSON> hodnot
RUN echo "session.save_path=\"/tmp\"" >> /usr/local/etc/php/php.ini && \
    echo "session.cookie_secure=\"0\"" >> /usr/local/etc/php/php.ini && \
    echo "session.cookie_lifetime=\"1209600\"" >> /usr/local/etc/php/php.ini && \
    echo "upload_max_filesize = 100M " >> /usr/local/etc/php/php.ini && \
    echo "post_max_size = 100M " >> /usr/local/etc/php/php.ini && \
    echo "memory_limit = 2048M " >> /usr/local/etc/php/php.ini && \
    echo "max_execution_time = 240 " >> /usr/local/etc/php/php.ini

# Xdebug temporarily disabled for troubleshooting
# RUN pecl install -f xdebug && docker-php-ext-enable xdebug
# COPY php/xdebug.ini /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini

# Pracovní složka kontejneru
WORKDIR /var/www/html