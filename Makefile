# Makefile pro správu Docker kontejnerů a závislostí

# Základní proměnné
DOCKER_COMPOSE = docker-compose
DOCKER_SERVICE = web

# Barvy pro výstup
GREEN = \033[0;32m
YELLOW = \033[1;33m
RED = \033[0;31m
NC = \033[0m # No Color

.PHONY: help up down build rebuild install-composer install-composer-fresh update-composer logs shell clean

# Vý<PERSON>zí příkaz - zobraz<PERSON> n<PERSON>ědu
help:
	@echo "$(GREEN)Dostupné příkazy:$(NC)"
	@echo "  $(YELLOW)make up$(NC)              - Spust<PERSON> Docker kontejnery"
	@echo "  $(YELLOW)make down$(NC)            - Zastaví Docker kontejnery"
	@echo "  $(YELLOW)make build$(NC)           - Sestav<PERSON>er image"
	@echo "  $(YELLOW)make rebuild$(NC)         - Znovu sestaví Docker image (bez cache)"
	@echo "  $(YELLOW)make install-composer$(NC) - Nainstaluje <PERSON> z<PERSON>"
	@echo "  $(YELLOW)make install-composer-fresh$(NC) - Vyřeš<PERSON> konflikty a nainstaluje závislosti"
	@echo "  $(YELLOW)make update-composer$(NC)  - Aktualizuje Composer závislosti"
	@echo "  $(YELLOW)make logs$(NC)            - Zobrazí logy kontejnerů"
	@echo "  $(YELLOW)make shell$(NC)           - Otevře shell v web kontejneru"
	@echo "  $(YELLOW)make clean$(NC)           - Vyčistí Docker systém"

# Spustí Docker kontejnery
up:
	@echo "$(GREEN)Spouštím Docker kontejnery...$(NC)"
	$(DOCKER_COMPOSE) up -d
	@echo "$(GREEN)Kontejnery jsou spuštěny!$(NC)"

# Zastaví Docker kontejnery
down:
	@echo "$(YELLOW)Zastavuji Docker kontejnery...$(NC)"
	$(DOCKER_COMPOSE) down
	@echo "$(GREEN)Kontejnery jsou zastaveny!$(NC)"

# Sestaví Docker image
build:
	@echo "$(GREEN)Sestavuji Docker image...$(NC)"
	$(DOCKER_COMPOSE) build
	@echo "$(GREEN)Image je sestaven!$(NC)"

# Znovu sestaví Docker image bez cache
rebuild:
	@echo "$(GREEN)Znovu sestavuji Docker image (bez cache)...$(NC)"
	$(DOCKER_COMPOSE) build --no-cache
	@echo "$(GREEN)Image je znovu sestaven!$(NC)"

# Nainstaluje Composer závislosti
install-composer:
	@echo "$(GREEN)Instaluji Composer závislosti...$(NC)"
	$(DOCKER_COMPOSE) run --rm $(DOCKER_SERVICE) composer install --no-dev --optimize-autoloader
	@echo "$(GREEN)Composer závislosti jsou nainstalovány!$(NC)"

# Vyřeší konflikty v composer.lock a nainstaluje závislosti
install-composer-fresh:
	@echo "$(YELLOW)Mažu composer.lock pro vyřešení konfliktů...$(NC)"
	rm -f composer.lock
	@echo "$(GREEN)Instaluji Composer závislosti s novým lock souborem...$(NC)"
	$(DOCKER_COMPOSE) run --rm $(DOCKER_SERVICE) composer install --no-dev --optimize-autoloader
	@echo "$(GREEN)Composer závislosti jsou nainstalovány s novým lock souborem!$(NC)"

# Aktualizuje Composer závislosti
update-composer:
	@echo "$(GREEN)Aktualizuji Composer závislosti...$(NC)"
	$(DOCKER_COMPOSE) run --rm $(DOCKER_SERVICE) composer update
	@echo "$(GREEN)Composer závislosti jsou aktualizovány!$(NC)"

# Zobrazí logy kontejnerů
logs:
	@echo "$(GREEN)Zobrazuji logy kontejnerů...$(NC)"
	$(DOCKER_COMPOSE) logs -f

# Otevře shell v web kontejneru
shell:
	@echo "$(GREEN)Otevírám shell v web kontejneru...$(NC)"
	$(DOCKER_COMPOSE) exec $(DOCKER_SERVICE) /bin/bash

# Vyčistí Docker systém
clean:
	@echo "$(YELLOW)Čistím Docker systém...$(NC)"
	docker system prune -f
	@echo "$(GREEN)Docker systém je vyčištěn!$(NC)"

# Kompletní setup - sestaví, spustí a nainstaluje závislosti
setup: build up install-composer
	@echo "$(GREEN)Kompletní setup je dokončen!$(NC)"

# Restart kontejnerů
restart: down up
	@echo "$(GREEN)Kontejnery jsou restartovány!$(NC)"
